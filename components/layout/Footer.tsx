'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import {
  Phone,
  Mail,
  MapPin,
  Clock,
  Facebook,
  Instagram,
  Youtube,
  MessageCircle,
  ArrowRight,
  Heart
} from 'lucide-react';
import { COMPANY_INFO, SOCIAL_LINKS, QUICK_LINKS } from '@/lib/constants';

export default function Footer() {
  const currentYear = new Date().getFullYear();


  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <footer className="bg-gray-900/95 backdrop-blur-xl text-white border-t border-gray-800/50">
      {/* Main Footer Content */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        className="container-custom py-16"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Company Info */}
          <motion.div variants={itemVariants} className="lg:col-span-1">
            {/* Logo Section with Partners */}
            <div className="flex items-center gap-6 mb-6">
              {/* Main Positive7 Logo */}
              <Link href="/" className="flex items-center">
                <div className="relative h-20 w-20">
                  <Image
                    src="/images/static/logos/positive7-logo.png"
                    alt={COMPANY_INFO.name}
                    fill
                    sizes="80px"
                    className="object-contain"
                  />
                </div>
              </Link>

              {/* Separator Line */}
              <div className="h-16 w-px bg-gray-600/50"></div>

              {/* Tourism Partner Logos */}
              <div className="flex items-center gap-4">
                <div className="relative h-16 w-24">
                  <Image
                    src="/images/static/logos/gujarat-tourism-logo.png"
                    alt="Gujarat Tourism"
                    fill
                    sizes="96px"
                    className="object-contain"
                  />
                </div>
                <div className="relative h-16 w-24">
                  <Image
                    src="/images/static/logos/GETOA.png"
                    alt="GETOA"
                    fill
                    sizes="96px"
                    className="object-contain"
                  />
                </div>
              </div>
            </div>

            <p className="text-gray-300 mb-6 leading-relaxed">
              Gujarat Tourism affiliated outbound experiential learning company organizing educational trips, student tours, CAS Projects, adventure camps & workshops.
            </p>

            {/* Social Links */}
            <div className="flex space-x-4">
              <Link
                href={{ pathname: SOCIAL_LINKS.facebook }}
                target="_blank"
                rel="noopener noreferrer"
                className="p-3 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-primary-600/80 hover:backdrop-blur-md transition-all duration-300 group hover:scale-110 hover:shadow-lg"
                aria-label="Facebook"
              >
                <Facebook className="h-5 w-5 group-hover:scale-110 transition-transform" />
              </Link>
              <Link
                href={{ pathname: SOCIAL_LINKS.instagram }}
                target="_blank"
                rel="noopener noreferrer"
                className="p-3 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gradient-to-r hover:from-purple-500/80 hover:to-pink-500/80 hover:backdrop-blur-md transition-all duration-300 group hover:scale-110 hover:shadow-lg"
                aria-label="Instagram"
              >
                <Instagram className="h-5 w-5 group-hover:scale-110 transition-transform" />
              </Link>
              <Link
                href={{ pathname: SOCIAL_LINKS.youtube }}
                target="_blank"
                rel="noopener noreferrer"
                className="p-3 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-red-600/80 hover:backdrop-blur-md transition-all duration-300 group hover:scale-110 hover:shadow-lg"
                aria-label="YouTube"
              >
                <Youtube className="h-5 w-5 group-hover:scale-110 transition-transform" />
              </Link>
              <Link
                href={{ pathname: SOCIAL_LINKS.whatsapp }}
                target="_blank"
                rel="noopener noreferrer"
                className="p-3 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-green-600/80 hover:backdrop-blur-md transition-all duration-300 group hover:scale-110 hover:shadow-lg"
                aria-label="WhatsApp"
              >
                <MessageCircle className="h-5 w-5 group-hover:scale-110 transition-transform" />
              </Link>
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div variants={itemVariants}>
            <h4 className="text-lg font-semibold mb-6 text-white">Quick Links</h4>
            <ul className="space-y-3">
              {QUICK_LINKS.map((link) => (
                <li key={link.name}>
                  <Link
                    href={{ pathname: link.href }}
                    className="text-gray-300 hover:text-secondary-400 transition-all duration-300 flex items-center group px-2 py-1 rounded-lg hover:bg-gray-800/30 hover:backdrop-blur-sm"
                  >
                    <ArrowRight className="h-4 w-4 mr-2 opacity-0 group-hover:opacity-100 transition-all duration-300 group-hover:scale-110" />
                    <span className="group-hover:translate-x-1 transition-transform duration-300">
                      {link.name}
                    </span>
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Contact Info */}
          <motion.div variants={itemVariants}>
            <h4 className="text-lg font-semibold mb-6 text-white">Contact Info</h4>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-secondary-400 mt-1 flex-shrink-0" />
                <div>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    904, Shivalik Highstreet, B/S ITC Narmada Hotel<br />
                    Vastrapur, Ahmedabad-380015
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-secondary-400 flex-shrink-0" />
                <div>
                  <Link
                    href={{ pathname: `tel:${COMPANY_INFO.phone}` }}
                    className="text-gray-300 hover:text-secondary-400 transition-all duration-300 hover:scale-105 inline-block"
                  >
                    {COMPANY_INFO.phone}
                  </Link>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-secondary-400 flex-shrink-0" />
                <Link
                  href={{ pathname: `mailto:${COMPANY_INFO.email}` }}
                  className="text-gray-300 hover:text-secondary-400 transition-all duration-300 hover:scale-105 inline-block"
                >
                  {COMPANY_INFO.email}
                </Link>
              </div>

              <div className="flex items-center space-x-3">
                <Clock className="h-5 w-5 text-secondary-400 flex-shrink-0" />
                <div className="text-gray-300 text-sm">
                  <p>Mon - Sat: 9:00 AM - 7:00 PM</p>
                  <p>Sunday: 10:00 AM - 5:00 PM</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.div>



      {/* Bottom Bar */}
      <div className="border-t border-gray-800/50 bg-gray-950/90 backdrop-blur-xl">
        <div className="container-custom py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-gray-400 text-sm text-center md:text-left">
              <p>
                © {currentYear} {COMPANY_INFO.name}. All rights reserved.
                <span className="hidden sm:inline"> | Gujarat Tourism Affiliated</span>
              </p>
            </div>

            <div className="flex items-center space-x-6 text-sm">
              <Link
                href={{ pathname: "/terms-conditions" }}
                className="text-gray-400 hover:text-secondary-400 transition-all duration-300 hover:scale-105"
              >
                Terms & Conditions
              </Link>
              <Link
                href={{ pathname: "/privacy-policy" }}
                className="text-gray-400 hover:text-secondary-400 transition-all duration-300 hover:scale-105"
              >
                Privacy Policy
              </Link>
              <div className="flex items-center text-gray-400">
                Made with <Heart className="h-4 w-4 mx-1 text-red-500" /> in India
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
