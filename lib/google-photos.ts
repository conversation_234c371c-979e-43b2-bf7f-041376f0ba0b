import { createAuthenticatedPhotosClient } from './google-photos-auth';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

/**
 * Upload a buffer to Google Photos and add to album
 * @param imageBuffer The image buffer to upload
 * @param fileName The filename for the image
 * @param albumId The Google Photos album ID
 * @param refreshToken The OAuth refresh token
 * @returns Promise with upload result
 */
export async function uploadToGooglePhotos(
  imageBuffer: Buffer,
  fileName: string,
  albumId: string,
  refreshToken: string
): Promise<{ mediaItemId: string; albumUrl?: string }> {
  try {
    console.log(`[GOOGLE_PHOTOS_UPLOAD] Starting upload: ${fileName}`);
    
    const { auth } = createAuthenticatedPhotosClient(refreshToken);
    
    // Get access token
    const { token } = await auth.getAccessToken();
    
    if (!token) {
      throw new Error('Failed to get access token');
    }
    
    console.log('[GOOGLE_PHOTOS_UPLOAD] Got access token, uploading bytes...');
    
    // Step 1: Upload the raw bytes
    const uploadResponse = await fetch('https://photoslibrary.googleapis.com/v1/uploads', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/octet-stream',
        'X-Goog-Upload-File-Name': fileName,
        'X-Goog-Upload-Protocol': 'raw',
      },
      body: imageBuffer,
    });
    
    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();
      throw new Error(`Upload failed: HTTP ${uploadResponse.status}: ${errorText}`);
    }
    
    const uploadToken = await uploadResponse.text();
    console.log('[GOOGLE_PHOTOS_UPLOAD] Bytes uploaded, creating media item...');
    
    // Step 2: Create the media item
    const createResponse = await fetch('https://photoslibrary.googleapis.com/v1/mediaItems:batchCreate', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        albumId: albumId,
        newMediaItems: [
          {
            description: `Uploaded by Positive7 Tourism - ${fileName}`,
            simpleMediaItem: {
              fileName: fileName,
              uploadToken: uploadToken,
            },
          },
        ],
      }),
    });
    
    if (!createResponse.ok) {
      const errorText = await createResponse.text();
      throw new Error(`Create media item failed: HTTP ${createResponse.status}: ${errorText}`);
    }
    
    const createData = await createResponse.json();
    
    if (!createData.newMediaItemResults?.[0]?.mediaItem?.id) {
      console.error('[GOOGLE_PHOTOS_UPLOAD] Create response:', createData);
      throw new Error('Failed to create media item - no ID returned');
    }
    
    const mediaItemId = createData.newMediaItemResults[0].mediaItem.id;
    console.log(`[GOOGLE_PHOTOS_UPLOAD] ✅ Media item created: ${mediaItemId}`);
    
    return {
      mediaItemId,
      albumUrl: `https://photos.google.com/album/${albumId}`
    };
    
  } catch (error) {
    console.error('[GOOGLE_PHOTOS_UPLOAD] ❌ Upload error:', error);
    throw new Error(`Failed to upload to Google Photos: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload a file to Google Photos and add to album
 * @param filePath Path to the file to upload
 * @param fileName The filename for the image
 * @param albumId The Google Photos album ID
 * @param refreshToken The OAuth refresh token
 * @returns Promise with upload result
 */
export async function uploadFileToGooglePhotos(
  filePath: string,
  fileName: string,
  albumId: string,
  refreshToken: string
): Promise<{ mediaItemId: string; albumUrl?: string }> {
  try {
    console.log(`[GOOGLE_PHOTOS_UPLOAD] Reading file: ${filePath}`);

    const fs = await import('fs');
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }

    const imageBuffer = await fs.promises.readFile(filePath);
    return await uploadToGooglePhotos(imageBuffer, fileName, albumId, refreshToken);
    
  } catch (error) {
    console.error('[GOOGLE_PHOTOS_UPLOAD] ❌ File upload error:', error);
    throw error;
  }
}

/**
 * Create a temporary file from buffer and upload to Google Photos
 * @param imageBuffer The image buffer
 * @param fileName The filename
 * @param albumId The Google Photos album ID
 * @param refreshToken The OAuth refresh token
 * @returns Promise with upload result
 */
export async function uploadBufferToGooglePhotos(
  imageBuffer: Buffer,
  fileName: string,
  albumId: string,
  refreshToken: string
): Promise<{ mediaItemId: string; albumUrl?: string }> {
  const tempDir = path.join(process.cwd(), 'tmp');
  const tempFilePath = path.join(tempDir, `${uuidv4()}_${fileName}`);
  
  try {
    // Use dynamic import for fs operations
    const fs = await import('fs');

    // Ensure temp directory exists
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Write buffer to temp file asynchronously
    await fs.promises.writeFile(tempFilePath, imageBuffer);

    // Upload the file
    const result = await uploadFileToGooglePhotos(tempFilePath, fileName, albumId, refreshToken);

    return result;

  } catch (error) {
    console.error('[GOOGLE_PHOTOS_UPLOAD] ❌ Buffer upload error:', error);
    throw error;
  } finally {
    // Clean up temp file
    try {
      const fs = await import('fs');
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
        console.log('[GOOGLE_PHOTOS_UPLOAD] Cleaned up temp file');
      }
    } catch (cleanupError) {
      console.warn('[GOOGLE_PHOTOS_UPLOAD] Failed to clean up temp file:', cleanupError);
    }
  }
}

/**
 * Get album details from Google Photos
 * @param albumId The album ID
 * @param refreshToken The OAuth refresh token
 * @returns Promise with album details
 */
export async function getGooglePhotosAlbumDetails(
  albumId: string,
  refreshToken: string
): Promise<{ id: string; title: string; productUrl: string; shareableUrl?: string }> {
  try {
    console.log(`[GOOGLE_PHOTOS_API] Getting album details: ${albumId}`);
    
    const { auth } = createAuthenticatedPhotosClient(refreshToken);
    
    // Get access token
    const { token } = await auth.getAccessToken();
    
    if (!token) {
      throw new Error('Failed to get access token');
    }
    
    const response = await fetch(`https://photoslibrary.googleapis.com/v1/albums/${albumId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Get album failed: HTTP ${response.status}: ${errorText}`);
    }
    
    const albumData = await response.json();
    
    return {
      id: albumData.id || albumId,
      title: albumData.title || 'Untitled Album',
      productUrl: albumData.productUrl || `https://photos.google.com/album/${albumId}`,
      shareableUrl: albumData.shareableUrl,
    };
    
  } catch (error) {
    console.error('[GOOGLE_PHOTOS_API] ❌ Error getting album details:', error);
    throw new Error(`Failed to get album details: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
