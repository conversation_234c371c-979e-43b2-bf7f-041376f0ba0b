// Gallery types for Positive7 Tourism Website

export interface Gallery {
  id: string;
  name: string;
  description: string | null;
  trip_id: string | null;
  folder_name: string | null;
  is_active: boolean;
  featured_image_url: string | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface GalleryImage {
  id: string;
  gallery_id: string;
  image_url: string;
  cloudinary_public_id: string;
  order_index: number | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface GalleryWithImages extends Gallery {
  gallery_images: GalleryImage[];
  image_count?: number;
  trips?: {
    id: string;
    title: string;
    destination: string;
    category: string | null;
  } | null;
}

export interface GalleryFormData {
  name: string;
  description?: string;
  trip_id?: string;
  folder_name?: string;
  is_active: boolean;
  featured_image_url?: string;
}

export interface GalleryImageFormData {
  image_url: string;
  cloudinary_public_id: string;
  order_index?: number;
}

// Queue upload interface for batch operations
export interface QueueUploadItem {
  id: string;
  file: File;
  preview: string;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  progress: number;
  error?: string;
  imageUrl?: string;
  cloudinaryPublicId?: string;
}

// For the public gallery page
export interface PublicGalleryImage {
  id: string;
  title: string;
  category: string;
  location: string;
  imageUrl: string;
  description: string;
  gallery_name?: string;
}

// API Response types
export interface GalleryListResponse {
  data: Gallery[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface GalleryResponse {
  data: Gallery;
}

export interface GalleryWithImagesResponse {
  data: GalleryWithImages;
}

export interface GalleryImageResponse {
  data: GalleryImage;
}

export interface GalleryImagesResponse {
  data: GalleryImage[];
}

// Filter and search types
export interface GalleryFilters {
  search?: string;
  trip_id?: string;
  is_active?: boolean;
  page?: number;
  limit?: number;
}

export interface GalleryImageFilters {
  gallery_id: string;
  page?: number;
  limit?: number;
}
