import { Metadata } from 'next'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import { Suspense } from 'react'
import { createServerSupabase } from '@/lib/supabase-server'
import TripsClient from '@/components/trips/TripsClient'
import TripsPageSkeleton from '@/components/trips/TripsPageSkeleton'
import { JsonLd } from '@/components/seo/JsonLd'
import CacheManager from '@/components/cache/CacheManager'
import { PageErrorBoundary } from '@/components/error/ComprehensiveErrorBoundary'

// Disable caching for real-time data
export const revalidate = 0

export const metadata: Metadata = {
  title: 'Explore Educational Tours & Adventures | Positive7 Tourism',
  description: 'Discover amazing educational tours and adventures across India with Positive7 Tourism. Find your perfect journey with customizable trips for students and groups.',
  keywords: 'educational tours, adventure trips, India travel, student trips, group tours, trekking, Positive7 Tourism',
  openGraph: {
    title: 'Educational Tours & Adventures | Positive7 Tourism',
    description: 'Discover educational tours and adventures across India with Positive7. Perfect for students, schools and groups.',
    type: 'website',
    url: 'https://positive7.org/trips',
    images: [
      {
        url: 'https://positive7.org/images/trips/trips-og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Positive7 Educational Tours'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Educational Tours & Adventures | Positive7 Tourism',
    description: 'Discover amazing educational tours and adventures across India with Positive7 Tourism.',
    images: ['https://res.cloudinary.com/peebst3r/image/upload/v1748754487/positive7/trips/Manali-River.jpg']
  }
}

// Fetch trips from the database
async function getTrips() {
  const supabase = createServerSupabase()
  
  const { data: trips, error } = await supabase
    .from('trips')
    .select('*')
    .eq('is_active', true)
    .order('created_at', { ascending: false })
    
  if (error) {
    console.error('Error fetching trips:', error)
    return []
  }
  
  // Use trips data directly since it matches the Trip interface
  return trips || []
}

export default async function TripsPage() {
  const trips = await getTrips()

  // Structured data for the trips collection page
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": "Educational Tours & Adventures",
    "description": "Discover amazing educational tours and adventures across India with Positive7 Tourism.",
    "url": "https://positive7.org/trips",
    "mainEntity": {
      "@type": "ItemList",
      "itemListElement": trips.slice(0, 10).map((trip, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "TouristTrip",
          "name": trip.title,
          "description": trip.description,
          "url": `https://positive7.org/trips/${trip.slug}`
        }
      }))
    }
  }

  return (
    <PageErrorBoundary context="trips-page">
      {/* Cache management for dynamic content */}
      <CacheManager addMetaTags={true} updateSW={true} />

      {/* Add JSON-LD structured data */}
      <JsonLd data={structuredData} />

      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
          <div className="max-w-7xl mx-auto px-4 py-8">
            <Suspense fallback={<TripsPageSkeleton />}>
              <TripsClient initialTrips={trips} />
            </Suspense>
          </div>
        </div>
      </main>
      <Footer />
    </PageErrorBoundary>
  )
}
