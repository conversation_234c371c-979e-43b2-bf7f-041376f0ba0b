import { <PERSON>ada<PERSON> } from 'next';
import { notFound } from 'next/navigation';
import { Suspense } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import EduTourService from '@/components/services/EduTourService';
import AdventuresService from '@/components/services/AdventuresService';
import GirlsGoSoloService from '@/components/services/GirlsGoSoloService';
import HolidaysService from '@/components/services/HolidaysService';
import { PageErrorBoundary } from '@/components/error/ComprehensiveErrorBoundary';
import CacheManager from '@/components/cache/CacheManager';

interface ServicePageProps {
  params: Promise<{
    service: string;
  }>;
}

const serviceMetadata = {
  'edutour': {
    title: 'Educational Tours - Positive7 EduTour',
    description: 'Experience learning beyond the classroom with our educational tours. Designed to broaden student thinking, provide meaningful experiences, and develop essential skills for confident progression.',
    keywords: 'educational tours, student trips, experiential learning, classroom extension, global diversity, learning outcomes',
  },
  'adventures': {
    title: 'Adventure Camps - Positive7 Adventures',
    description: 'Develop key skills through outdoor adventures and camping experiences. Our programs focus on team building, personal development, expeditions, and survival skills in challenging environments.',
    keywords: 'adventure camps, outdoor education, team building, camping, expeditions, survival skills, personal development',
  },
  'girls-go-solo': {
    title: 'Women Solo Travel - Positive7 Girls Go Solo',
    description: 'Empowering women through safe, supportive solo travel experiences. Break stereotypes, build confidence, and discover your true self with our women-only travel programs.',
    keywords: 'women solo travel, girls travel, female empowerment, safe travel, women adventures, solo trips',
  },
  'holidays': {
    title: 'Holiday Packages - Positive7 Holidays',
    description: 'Comprehensive holiday packages for families and groups. Creating unforgettable memories through carefully crafted vacation experiences. Coming soon!',
    keywords: 'holiday packages, family vacations, group travel, vacation planning, travel packages',
  },
};

const validServices = ['edutour', 'adventures', 'girls-go-solo', 'holidays'];

export async function generateMetadata({ params }: ServicePageProps): Promise<Metadata> {
  const { service } = await params;
  
  if (!validServices.includes(service)) {
    return {
      title: 'Service Not Found - Positive7',
      description: 'The requested service page could not be found.',
    };
  }

  const metadata = serviceMetadata[service as keyof typeof serviceMetadata];
  
  return {
    title: metadata.title,
    description: metadata.description,
    keywords: metadata.keywords,
    openGraph: {
      title: metadata.title,
      description: metadata.description,
      type: 'website',
      images: [
        {
          url: `/images/services/${service}-hero.jpg`,
          width: 1200,
          height: 630,
          alt: metadata.title,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: metadata.title,
      description: metadata.description,
      images: [`/images/services/${service}-hero.jpg`],
    },
  };
}

export async function generateStaticParams() {
  return validServices.map((service) => ({
    service,
  }));
}

function getServiceComponent(service: string) {
  switch (service) {
    case 'edutour':
      return <EduTourService />;
    case 'adventures':
      return <AdventuresService />;
    case 'girls-go-solo':
      return <GirlsGoSoloService />;
    case 'holidays':
      return <HolidaysService />;
    default:
      return null;
  }
}

export default async function ServicePage({ params }: ServicePageProps) {
  const { service } = await params;
  
  if (!validServices.includes(service)) {
    notFound();
  }

  const ServiceComponent = getServiceComponent(service);
  
  if (!ServiceComponent) {
    notFound();
  }

  return (
    <PageErrorBoundary context={`service-${service}-page`}>
      {/* Cache management for dynamic content */}
      <CacheManager addMetaTags={true} updateSW={true} />

      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-coral-50 via-orange-50 to-teal-50">
          <Suspense fallback={
            <div className="flex items-center justify-center min-h-screen">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
            </div>
          }>
            {ServiceComponent}
          </Suspense>
        </div>
      </main>
      <Footer />
    </PageErrorBoundary>
  );
}
