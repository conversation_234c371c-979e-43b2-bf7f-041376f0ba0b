import { NextRequest, NextResponse } from 'next/server';
import sharp from 'sharp';
import fs from 'fs-extra';
import path from 'path';
// Google Drive support removed - only Google Photos OAuth supported
import { createServerSupabase } from '@/lib/supabase-server';

/**
 * Helper function to get the logo buffer, either from filesystem or by fetching from URL
 */
async function getLogoBuffer(): Promise<Buffer> {
  // Try local filesystem first (works in development)
  const logoPath = path.join(process.cwd(), 'public', 'images', 'static', 'logos', 'positive7-logo.png');
  
  try {
    return await fs.readFile(logoPath);
  } catch (err) {
    console.log('Could not load logo from filesystem, trying URL fetch');
    
    // Determine the appropriate URL based on environment
    let logoUrl;
    if (process.env.VERCEL_URL) {
      // We're on Vercel
      logoUrl = `https://${process.env.VERCEL_URL}/images/static/logos/positive7-logo.png`;
    } else if (process.env.NEXT_PUBLIC_SITE_URL) {
      // Custom domain is set
      logoUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/images/static/logos/positive7-logo.png`;
    } else {
      // Fallback to localhost
      logoUrl = 'http://localhost:3000/images/static/logos/positive7-logo.png';
    }
    
    console.log(`Fetching logo from: ${logoUrl}`);
    const response = await fetch(logoUrl);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch logo: ${response.statusText}`);
    }
    
    const arrayBuffer = await response.arrayBuffer();
    return Buffer.from(arrayBuffer);
  }
}

// POST /api/admin/trips-photos/watermark - Add watermark and upload to Google Photos
// NOTE: No rate limiting applied - admins need to upload large batches of images (200-250 at a time)
// This endpoint handles watermarking and Google Photos upload for trip photo albums
export async function POST(req: NextRequest) {
  try {
    // Parse form data
    const formData = await req.formData();
    const imageFile = formData.get('image') as File;
    const tripPhotoDetailsId = formData.get('tripPhotoDetailsId') as string;

    if (!imageFile) {
      return NextResponse.json(
        { message: 'No image file provided' },
        { status: 400 }
      );
    }

    // Check file size (10MB max)
    const MAX_SIZE = 10 * 1024 * 1024; // 10MB
    if (imageFile.size > MAX_SIZE) {
      return NextResponse.json(
        { message: 'Image file too large. Maximum size is 10MB' },
        { status: 400 }
      );
    }

    // Check file type
    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!validTypes.includes(imageFile.type)) {
      return NextResponse.json(
        { message: 'Invalid file type. Only JPEG, PNG, GIF, and WebP are supported' },
        { status: 400 }
      );
    }

    // Get the buffer from the file
    const buffer = Buffer.from(await imageFile.arrayBuffer());

    // Get the logo buffer using our helper function
    const logoBuffer = await getLogoBuffer();
    
    // Get image dimensions
    const metadata = await sharp(buffer).metadata();
    const width = metadata.width || 800;
    
    // Resize logo to be proportional to the image width (15% of width)
    const logoWidth = Math.round(width * 0.15);
    const resizedLogo = await sharp(logoBuffer)
      .resize(logoWidth)
      .toBuffer();
    
    // Get resized logo metadata
    const logoMetadata = await sharp(resizedLogo).metadata();
    
    // Add watermark to top right with padding
    const padding = Math.round(width * 0.02); // 2% of width as padding
    
    // Process image with watermark
    const watermarkedBuffer = await sharp(buffer)
      .composite([
        {
          input: resizedLogo,
          top: padding,
          left: (width - (logoMetadata.width || 0)) - padding,
        },
      ])
      .toBuffer();

    // Generate unique filename with normalized extension
    const timestamp = Date.now();
    const ext = path.extname(imageFile.name).toLowerCase() || '.jpg';
    const filename = `watermarked_${timestamp}${ext}`;

    // Instead of writing to filesystem, convert to base64 for response
    const base64Image = `data:${imageFile.type};base64,${watermarkedBuffer.toString('base64')}`;

    // Get the trip photo details to check for storage settings
    const supabase = createServerSupabase();
    const { data: rawTripPhotoDetails, error } = await supabase
      .from('trip_photos_details')
      .select('*')
      .eq('id', tripPhotoDetailsId)
      .single();

    // Handle missing album record gracefully
    if (error || !rawTripPhotoDetails) {
      console.error('[WATERMARK] Album record not found:', error);
      return NextResponse.json({
        message: 'Image processed successfully (album not found)',
        imageUrl: base64Image,
        driveUrl: null,
        tripPhotoDetailsId
      });
    }

    // Cast to proper type with all fields
    const tripPhotoDetails = rawTripPhotoDetails as any;

    // Handle Google Photos OAuth upload (only supported storage type)
    const driveUrl = null; // Legacy field - kept for API compatibility

    if (tripPhotoDetails?.storage_type === 'google_photos_oauth' && tripPhotoDetails.oauth_refresh_token) {
      try {
        // Determine MIME type based on normalized file extension
        const mimeType =
          ext === '.png' ? 'image/png' :
          ext === '.gif' ? 'image/gif' :
          ext === '.webp' ? 'image/webp' :
          'image/jpeg'; // Default

        console.log(`[WATERMARK] Google Photos OAuth upload detected`);

        try {
          // Import Google Photos API functions
          const { uploadBufferToGooglePhotosAlbum } = await import('@/lib/google-photos-api');

          // Determine album title from trip name
          const albumTitle = `${tripPhotoDetails.trip_name} - Photos`;

          // Upload to Google Photos using OAuth with buffer (serverless-compatible)
          const result = await uploadBufferToGooglePhotosAlbum(
            tripPhotoDetails.oauth_refresh_token,
            watermarkedBuffer,
            filename,
            mimeType,
            tripPhotoDetails.google_photos_album_id || undefined,
            albumTitle
          );

          console.log(`[WATERMARK] Uploaded to Google Photos successfully: ${result.albumUrl}`);

          // Update database with album information
          const { error: albumUpdateError } = await supabase
            .from('trip_photos_details')
            .update({
              google_photos_album_id: result.albumId,
              updated_at: new Date().toISOString()
            })
            .eq('id', tripPhotoDetailsId);

          if (albumUpdateError) {
            console.error('[WATERMARK] Error updating album info:', albumUpdateError);
          }
        } catch (photosError) {
          console.error('[WATERMARK] Error uploading to Google Photos:', photosError);
        }
      } catch (uploadError: any) {
        console.error('[WATERMARK] Error uploading to Google Photos:', uploadError);
        // We don't throw here - we'll return the base64 image if upload fails
      }
    }

    return NextResponse.json({
      message: 'Image processed successfully',
      imageUrl: base64Image,
      driveUrl,
      tripPhotoDetailsId
    });
    
  } catch (error: any) {
    console.error('Error processing image:', error);
    return NextResponse.json(
      { message: 'Failed to process image', error: error.message },
      { status: 500 }
    );
  }
} 